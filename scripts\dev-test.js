#!/usr/bin/env node

/**
 * Quick development test script
 * Usage: node scripts/dev-test.js [provider] [model] [prompt]
 */

import { spawn } from 'child_process';
import path from 'path';

const args = process.argv.slice(2);
const provider = args[0] || 'openai';
const model = args[1] || 'gpt-3.5-turbo';
const prompt = args[2] || 'Hello, how are you?';

console.log(`🚀 Testing with provider: ${provider}, model: ${model}`);
console.log(`💬 Prompt: "${prompt}"`);
console.log('─'.repeat(50));

// Set test environment variables if not already set
const testEnvVars = {
  OPENAI_API_KEY: process.env.OPENAI_API_KEY || 'test-openai-key',
  ANTHROPIC_API_KEY: process.env.ANTHROPIC_API_KEY || 'test-anthropic-key',
  MISTRAL_API_KEY: process.env.MISTRAL_API_KEY || 'test-mistral-key',
  OPENROUTER_API_KEY: process.env.OPENROUTER_API_KEY || 'test-openrouter-key',
  CUSTOM_API_KEY: process.env.CUSTOM_API_KEY || 'test-custom-key',
  CUSTOM_BASE_URL: process.env.CUSTOM_BASE_URL || 'http://localhost:3001/v1',
  DEBUG: '1' // Enable debug mode
};

// Build command
const geminiArgs = [
  'scripts/start.js',
  '--provider', provider,
  '--model', model,
  '--prompt', prompt,
  '--debug'
];

console.log(`🔧 Running: node ${geminiArgs.join(' ')}`);
console.log('─'.repeat(50));

// Spawn the process
const child = spawn('node', geminiArgs, {
  stdio: 'inherit',
  env: { ...process.env, ...testEnvVars },
  cwd: path.resolve(process.cwd())
});

child.on('error', (error) => {
  console.error('❌ Error:', error.message);
});

child.on('close', (code) => {
  console.log(`\n🏁 Process exited with code ${code}`);
});
