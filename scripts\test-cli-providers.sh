#!/bin/bash

# Quick CLI Provider Test Script
# Usage: chmod +x scripts/test-cli-providers.sh && ./scripts/test-cli-providers.sh

echo "🚀 Testing Gemini CLI Provider Support..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test function
test_provider() {
    local provider=$1
    local model=$2
    local env_var=$3
    local test_key=$4
    
    echo -e "\n${BLUE}Testing $provider provider...${NC}"
    
    # Set test API key
    export $env_var="$test_key"
    
    # Test help command first (should work without API call)
    echo "  📋 Testing help command..."
    if npm run start -- --provider $provider --help > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Help command works${NC}"
    else
        echo -e "  ${RED}❌ Help command failed${NC}"
        return 1
    fi
    
    # Test version command
    echo "  📋 Testing version command..."
    if npm run start -- --provider $provider --version > /dev/null 2>&1; then
        echo -e "  ${GREEN}✅ Version command works${NC}"
    else
        echo -e "  ${RED}❌ Version command failed${NC}"
        return 1
    fi
    
    echo -e "  ${GREEN}✅ $provider provider basic tests passed${NC}"
    return 0
}

# Build the project first
echo "🔨 Building project..."
if npm run build > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Build successful${NC}"
else
    echo -e "${RED}❌ Build failed${NC}"
    exit 1
fi

# Test each provider
echo -e "\n${YELLOW}Testing provider argument parsing...${NC}"

test_provider "openai" "gpt-3.5-turbo" "OPENAI_API_KEY" "test-openai-key"
test_provider "anthropic" "claude-3-sonnet" "ANTHROPIC_API_KEY" "test-anthropic-key"
test_provider "mistral" "mistral-large" "MISTRAL_API_KEY" "test-mistral-key"
test_provider "openrouter" "anthropic/claude-3-sonnet" "OPENROUTER_API_KEY" "test-openrouter-key"

# Test custom provider with mock server
echo -e "\n${BLUE}Testing custom provider with mock server...${NC}"
export CUSTOM_API_KEY="test-custom-key"
export CUSTOM_BASE_URL="http://localhost:3001/v1"

# Start mock server in background
echo "  🤖 Starting mock server..."
npm run test:mock-server > /dev/null 2>&1 &
MOCK_PID=$!
sleep 3

# Test custom provider
if test_provider "custom" "mock-model" "CUSTOM_API_KEY" "test-custom-key"; then
    echo -e "  ${GREEN}✅ Custom provider test passed${NC}"
else
    echo -e "  ${RED}❌ Custom provider test failed${NC}"
fi

# Clean up mock server
kill $MOCK_PID > /dev/null 2>&1

echo -e "\n${GREEN}🎉 All provider tests completed!${NC}"
echo -e "${YELLOW}💡 To test with real APIs, set the appropriate environment variables:${NC}"
echo "   export OPENAI_API_KEY='your-real-key'"
echo "   export ANTHROPIC_API_KEY='your-real-key'"
echo "   export MISTRAL_API_KEY='your-real-key'"
echo "   export OPENROUTER_API_KEY='your-real-key'"
